# Commercial Use Guidelines

## Commercial Attribution Requirement

MAIAChat Desktop is licensed under the MIT License with a **mandatory commercial attribution clause**. This means:

### ✅ Free Personal Use
- No attribution required for personal, educational, or non-commercial use
- Full MIT license freedoms apply

### ⚠️ Commercial Use Requires Attribution
- **All commercial use** must include prominent attribution
- Attribution protects the creator's brand and drives traffic to MAIAChat.com
- Helps users discover the original project and YouTube channel

## Required Attribution

### Exact Text Required
```
"Powered by MAIAChat.com Desktop by <PERSON><PERSON><PERSON><PERSON>"
```

### Placement Options (Choose One)
1. **Application UI** - About dialog, help menu, or footer
2. **Documentation** - README, user manual, or website
3. **Marketing** - Product pages, brochures, or announcements

### Additional Recommended Links
While not required, including these links helps users discover more resources:
- Website: https://maiachat.com
- YouTube: https://www.youtube.com/@AIexTheAIWorkbench
- GitHub: https://github.com/alexcelewicz/MAIAChat-Desktop

## Commercial Licensing Without Attribution

### Available Options
For commercial use without attribution requirements, contact Aleksan<PERSON> for:
- **Enterprise License** - No attribution required
- **OEM License** - For hardware/software bundling
- **Custom License** - Tailored terms for specific use cases

### Pricing Structure
- **Startup/Small Business**: Negotiable based on revenue
- **Enterprise**: Annual licensing available
- **OEM/Reseller**: Volume discounts available
- **Custom Solutions**: Project-based pricing

### Contact for Commercial Licensing
- **Primary**: Through GitHub repository issues
- **Website**: Contact form at MAIAChat.com
- **YouTube**: Community tab for business inquiries

## Brand Protection Measures

### Trademark Considerations
- "MAIAChat" is a trademark of Aleksander Celewicz
- Commercial users may not claim ownership of the MAIAChat brand
- Attribution helps protect brand integrity

### Quality Assurance
- Commercial attribution ensures users can find official support
- Helps maintain quality standards across implementations
- Provides feedback channel for improvements

### Community Building
- Attribution drives traffic to the YouTube channel for tutorials
- Helps build a community around the project
- Supports continued development and maintenance

## Compliance Monitoring

### Automated Detection
- GitHub monitors for forks and derivatives
- Web crawling for attribution compliance
- Community reporting of violations

### Enforcement Process
1. **Friendly Contact** - Initial outreach for compliance
2. **Formal Notice** - Written compliance request
3. **Legal Action** - If necessary for persistent violations

### Good Faith Compliance
- Immediate compliance efforts are appreciated
- Retroactive attribution is acceptable
- Contact us if you need help with implementation

## Benefits of Compliance

### For Users
- Access to official support and documentation
- Connection to active community and tutorials
- Updates and security notifications

### For Developers
- Legal protection and clear licensing terms
- Support for continued project development
- Access to commercial licensing options

### For the Ecosystem
- Sustainable open source development model
- Quality assurance through attribution
- Community growth and knowledge sharing

## Implementation Examples

### Web Application Footer
```html
<footer>
  <p>Powered by <a href="https://maiachat.com">MAIAChat.com Desktop</a> 
     by Aleksander Celewicz</p>
</footer>
```

### Desktop Application About Dialog
```python
about_text = """
MyApp v1.0.0
Powered by MAIAChat.com Desktop by Aleksander Celewicz
Visit: https://maiachat.com
"""
```

### API Documentation
```markdown
## Credits
This API is powered by MAIAChat.com Desktop by Aleksander Celewicz.
Learn more: https://maiachat.com
```

### Mobile App Settings
```
Settings > About > Credits
"Powered by MAIAChat.com Desktop by Aleksander Celewicz"
```

## Frequently Asked Questions

### Q: Why is commercial attribution required?
**A**: Attribution helps sustain the project by driving traffic to MAIAChat.com and the YouTube channel, enabling continued development and support.

### Q: Can I modify the attribution text?
**A**: No, the exact text must be used to ensure consistency and brand protection.

### Q: What if my app already has many attributions?
**A**: The attribution can be included in a credits section alongside other acknowledgments.

### Q: Is internal business use considered commercial?
**A**: Yes, any use within a for-profit organization requires attribution.

### Q: Can I get a license without attribution?
**A**: Yes, commercial licenses without attribution are available. Contact Aleksander Celewicz for pricing.

## Legal Framework

### License Authority
- The LICENSE file contains the authoritative terms
- This document provides implementation guidance
- MIT License base with commercial attribution clause

### Jurisdiction
- License governed by applicable copyright law
- International copyright protections apply
- DMCA and similar frameworks for enforcement

### Dispute Resolution
- Good faith negotiation preferred
- Mediation available for complex cases
- Legal action reserved for persistent violations

---

**Remember**: Attribution is a small requirement that helps sustain open source development while providing you with powerful AI tools for free. Thank you for supporting the MAIAChat community!

**Contact**: For questions about commercial use or licensing, reach out through GitHub, MAIAChat.com, or the YouTube channel.
