# License Compliance Guide

## Overview

MAIAChat Desktop is licensed under the MIT License with additional commercial use attribution requirements. This document provides clear guidance on compliance requirements for different use cases.

## License Summary

- **License Type**: MIT License with Commercial Attribution Clause
- **Copyright**: © 2025 Al<PERSON>san<PERSON>
- **Repository**: https://github.com/alexcelewicz/MAIAChat-Desktop
- **Website**: https://maiachat.com
- **YouTube Channel**: https://www.youtube.com/@AIexTheAIWorkbench

## Usage Categories

### ✅ Personal Use (No Attribution Required)
- Individual use for personal projects
- Educational purposes and learning
- Research and experimentation
- Non-commercial hobby projects

### ✅ Open Source Projects (Standard MIT)
- Contributing to open source projects
- Creating derivative open source works
- Academic research publications
- Non-profit organization use

### ⚠️ Commercial Use (Attribution Required)
Any commercial use requires prominent attribution as specified below.

## Commercial Use Attribution Requirements

### Required Attribution Text
For any commercial use, the following attribution must be prominently displayed:

```
"Powered by MAIAChat.com Desktop by <PERSON><PERSON><PERSON><PERSON>"
```

### Attribution Placement Requirements

**Choose ONE of the following placement options:**

1. **Application Interface** (Preferred)
   - About dialog or credits section
   - Help menu or information panel
   - Startup splash screen
   - Footer of main application window

2. **Documentation**
   - README.md or main documentation
   - User manual or help documentation
   - Installation guide
   - API documentation (if applicable)

3. **Marketing Materials**
   - Product website or landing page
   - Marketing brochures or presentations
   - Press releases or announcements
   - Social media posts about the product

### Attribution Examples

#### Application About Dialog
```
About MyApp
Version 1.0.0

Powered by MAIAChat.com Desktop by Aleksander Celewicz
Visit: https://maiachat.com
```

#### README.md
```markdown
## Credits
This application is powered by MAIAChat.com Desktop by Aleksander Celewicz.
Learn more at: https://maiachat.com
```

#### Website Footer
```html
<footer>
  Powered by <a href="https://maiachat.com">MAIAChat.com Desktop</a> 
  by Aleksander Celewicz
</footer>
```

## Commercial Licensing Options

### Standard Commercial Use
- Use the software with required attribution
- Modify and distribute with attribution
- Include in commercial products with attribution
- **Cost**: Free with attribution

### Commercial License Without Attribution
For commercial use without attribution requirements:
- Contact: Aleksander Celewicz
- Email: [Contact through GitHub or MAIAChat.com]
- Custom licensing terms available
- Negotiable pricing based on use case

## Compliance Checklist

### Before Commercial Deployment
- [ ] Review license terms in LICENSE file
- [ ] Implement required attribution in chosen location
- [ ] Verify attribution is prominently displayed
- [ ] Test attribution visibility in production
- [ ] Document compliance in internal records

### For Derivative Works
- [ ] Maintain original copyright notice
- [ ] Include LICENSE file in distribution
- [ ] Add attribution for commercial use
- [ ] Document modifications made
- [ ] Respect all original license terms

### For Redistribution
- [ ] Include complete LICENSE file
- [ ] Maintain all copyright notices
- [ ] Provide attribution if commercial
- [ ] Include this compliance guide
- [ ] Document any modifications

## Frequently Asked Questions

### Q: What constitutes "commercial use"?
**A**: Any use that generates revenue, including:
- Selling software that includes MAIAChat Desktop
- Using in commercial services or products
- Internal business use in for-profit companies
- Consulting services using the software

### Q: Is attribution required for internal business use?
**A**: Yes, if your organization is for-profit, attribution is required even for internal use.

### Q: Can I modify the attribution text?
**A**: No, the attribution text must be used exactly as specified. However, you may add additional styling or formatting.

### Q: Where can I get a commercial license without attribution?
**A**: Contact Aleksander Celewicz through GitHub or MAIAChat.com for commercial licensing options.

### Q: What if I forget to include attribution?
**A**: Add the attribution as soon as possible. Continued use without attribution may result in license violation.

## Enforcement and Contact

### License Violations
- License violations will be addressed through appropriate legal channels
- Good faith efforts to comply will be considered
- Contact us immediately if you discover a compliance issue

### Questions and Support
- **GitHub Issues**: https://github.com/alexcelewicz/MAIAChat-Desktop/issues
- **Website**: https://maiachat.com
- **YouTube**: https://www.youtube.com/@AIexTheAIWorkbench

### Commercial Licensing Inquiries
For commercial licensing without attribution requirements, please contact Aleksander Celewicz with:
- Your use case description
- Expected distribution volume
- Timeline for implementation
- Preferred licensing terms

## Legal Notice

This compliance guide is provided for informational purposes. The LICENSE file contains the authoritative license terms. In case of any conflict between this guide and the LICENSE file, the LICENSE file takes precedence.

---

**Last Updated**: January 30, 2025  
**Version**: 1.0.0  
**License Version**: MIT with Commercial Attribution Clause
