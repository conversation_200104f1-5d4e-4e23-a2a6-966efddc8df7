{"agent_count": 3, "general_instructions": "You are a team of three agents working on research of RAG retrieval quality. Agent 1 has direct access to RAG and is the only one that can read data from knowledge base, Agent two is then to provide quality response based on what agent 1 provided and Agent 3 is only agent that has access to correct data and who will check correctness of previous responses and provide report on quality of retrieved data. Agent 1 has access to around 1000 pages of the documentation from 3 large files. The aim is to retrive accurate data that is provided on single page based on prompt that is asking for content from this page.", "knowledge_base_path": "knowledge_base", "agents": [{"provider": "LM Studio", "model": "google/gemma-3-1b", "instructions": "You are Agent 1, the specialized data retriever in a three-agent RAG quality assessment system. Your mission is to locate and extract the most relevant information from a knowledge base containing approximately 1000 pages across 3 large documentation files.\n", "thinking_enabled": false}, {"provider": "Google GenAI", "model": "gemini-2.5-flash-preview-05-20", "instructions": "You are Agent 2, the information synthesizer responsible for transforming Agent 1's retrieved content into a comprehensive, accurate, and well-structured response. You serve as the critical bridge between raw retrieval and user-facing output.\nCore Responsibilities\nContext Integration: Combine multiple text chunks from Agent 1 into a coherent narrative\nGap Identification: Recognize when retrieved information is insufficient and communicate this clearly\nStructure Optimization: Organize information logically to best address the user's specific request\nAccuracy Maintenance: Ensure all statements can be directly traced back to Agent 1's provided context\nCritical Output Requirements\nInformation Fidelity\nStrict Source Adherence: Use ONLY information provided by Agent 1 - no external knowledge\nDirect Attribution: Every claim must be traceable to the retrieved context\nNo Extrapolation: Avoid drawing conclusions beyond what's explicitly stated in the context\nUncertainty Acknowledgment: When context is incomplete, state this explicitly: \"Based on the available information, [answer], however, the context does not provide details about [missing aspect].\"\nResponse Structure\nDirect Address: Answer the user's question immediately and completely\nLogical Organization: Present information in a clear, hierarchical structure\nComprehensive Coverage: Address all aspects of the user's query that are covered in the context\nEvidence Presentation: Include relevant quotes or specific details from the source material\nQuality Safeguards\nCompleteness Assessment: Explicitly state if the context is insufficient: \"The provided context does not contain enough information to fully answer your question about [specific aspect].\"\nConflicting Information: If Agent 1's chunks contain contradictory information, acknowledge this and present both perspectives\nScope Limitations: Clearly indicate the boundaries of what can be answered based on available context", "thinking_enabled": false}, {"provider": "Google GenAI", "model": "gemini-2.5-flash-preview-05-20", "instructions": "\nYou are Agent 3, the quality assurance specialist with exclusive access to ground truth data. Your role is to provide a clear, concise, and easily scannable assessment of the RAG pipeline's performance.\n\nYour primary goal is to make the evaluation easy to read and the scores immediately visible.\n\n**Evaluation Metrics (1-5 scale):**\n\n* **Retrieval Precision:** How relevant was the retrieved information? (Agent 1)\n* **Retrieval Recall:** Was all the relevant information retrieved? (Agent 1)\n* **Synthesis Accuracy:** Was the final response factually correct? (Agent 2)\n* **Synthesis Completeness:** Did the response answer all parts of the query? (Agent 2)\n* **Information Faithfulness:** Did the response stick to the provided information? (Agent 2)\n* **Response Coherence:** Was the response well-structured and easy to understand? (Agent 2)\n\n**Output Format:**\n\nYour entire response MUST be in the following Markdown format. Do not add any extra commentary before or after the formatted output.\n\n```markdown\n## RAG Performance Evaluation\n\n**Overall Score**: [Average of all scores]/5\n\n| Metric                 | Score (1-5) |\n|------------------------|-------------|\n| Retrieval Precision    | [Score]     |\n| Retrieval Recall       | [Score]     |\n| Synthesis Accuracy     | [Score]     |\n| Synthesis Completeness | [Score]     |\n| Information Faithfulness| [Score]     |\n| Response Coherence     | [Score]     |\n\n---\n\n### Key Observations & Recommendations\n\n**Strengths**:\n* [Briefly describe a key strength, with a specific example if possible]\n* [Briefly describe another strength]\n\n**Areas for Improvement**:\n* [Briefly describe a key weakness, with a specific example if possible]\n* [Briefly describe another weakness]\n\n**Recommendation**:\n* [Provide a single, concise recommendation for system improvement]\n\nGround Truth Context:\nBox 6.2 Smart systems, shared goals: The complementarity of artificial intelligence and digital public infrastructure Traditionally, infrastructure has been associated with physical assets such as roads, electricity grids and water systems that provide essential services for public use. Digital public infrastructure is a multidimensional approach to national digital transformation that relies on both physical and virtual systems. At its core, digital public infrastructure is about building and managing digital systems that support essential services in today’s society. These systems include proving one’s identity online, sending money quickly and securely and sharing information safely—with the right privacy protections and consent.1 Services aim to be inclusive so no one is left out, foundational so others can build on them, interoperable through open standards that can support diverse uses and publicly accountable to ensure they serve the public interest rather than private or siloed goals.2 Digital public infrastructure can speed up the use of AI. Many AI applications need both unstructured and structured data. Structured data often come from different government registries and databases, which are usually spread across ministries, departments and agencies. For example, in India AI is helping farmers get real-time support, including access to insurance and subsidies in their local languages—something that depends on combining many different data sources.3 AI can enhance digital public infrastructure. Unlike traditional infrastructure, digital public infrastructure is highly scalable, adaptable and reusable, offering unprecedented innovation potential. For instance, Stripe—a global payments platform—uses machine learning to spot signs of fraud by analysing unusual transaction patterns, shifts in purchasing behaviour and changes in device details.4 Similarly, AI powers biometric authentication in digital ID systems, which is especially useful where fingerprint recognition does not work well. This approach has been promoting inclusion, as, for example, many agricultural and manual workers face fingerprint erosion, making alternative biometric methods more reliable.5 Despite the growing potential, research on the causal links between digital public infrastructure and AI remains limited. More work is needed to understand how these two concepts can reinforce each other, what risks their interaction may pose and how policymakers should approach their integration, ensuring that benefits are widely distributed and reinforcing human agency, trust and fairness in the digital age.6 Notes 1. Eaves and Sandman 2021. 2. Eaves, Mazzucato and Vasconcellos 2024. 3. D’Silva and others 2019. 4. Adams 2025. 5. Digital public infrastructure can be vulnerable to serious threats, such as disinformation campaigns that undermine public confidence. A notable example comes from Brazil, where false information about a new regulation related to Pix—an instant digital payment platform—circulated widely, impacting more than 9.4 million people in 2025 (Luciano and Fleck 2025). 6. Rikap 2024.\nadvancements have tended to be accompanied by larger capital investment and higher capital shares of income.58 The relevant lens for tax policy may thus involve rebalancing capital and labour taxation to equitably distribute productivity gains and encourage investment in labour-complementing technology.59 The design of such taxation matters and should be carefully considered. For example, while taxing specific technologies—for example, a “robot tax”—may hamper innovation in a particular field,60 broader instruments such as capital income tax may achieve both efficiency and equity.61 AI itself can be leveraged as a tool for improving tax revenue by enhancing compliance and increasing administrative efficiency. AI-driven tools can help governments monitor complex financial transactions, detect fraud and reduce evasion.62 Strengthening tax systems is important for developing economies, which struggle with closing tax revenue gaps and increasing the tax-to-GDP ratio beyond 15 percent—a threshold associated with positive effects of taxation on economic growth and development.63 Expanding fiscal space through improved revenue collection can in turn fund critical complementarity investment—in education, skills development and digital infrastructure. Beyond taxation public investment in research and development of labour-enhancing AI, along with strategic subsidies for firms to adopt these types of technologies, can tip the balance towards AI as an enabler for augmentation and innovation.64 Public–private partnerships can drive labour-enhancing AI innovation and bridge gaps between research and development, business cases and societal needs (see box 6.4 later in the chapter). For example, in Mexico a newly established private sector–academia collaboration,\n```\n", "thinking_enabled": false}]}